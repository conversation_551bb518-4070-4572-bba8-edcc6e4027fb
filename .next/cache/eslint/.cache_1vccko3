[{"/Users/<USER>/Dev/ascenda/src/app/layout.tsx": "1", "/Users/<USER>/Dev/ascenda/src/app/page.tsx": "2"}, {"size": 678, "mtime": 1753965064341, "results": "3", "hashOfConfig": "4"}, {"size": 321, "mtime": 1753965052736, "results": "5", "hashOfConfig": "4"}, {"filePath": "6", "messages": "7", "suppressedMessages": "8", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16aeyvm", {"filePath": "9", "messages": "10", "suppressedMessages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Dev/ascenda/src/app/layout.tsx", [], [], "/Users/<USER>/Dev/ascenda/src/app/page.tsx", [], []]