{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Dev/ascenda/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\n\n// Hero Section Component\nfunction HeroSection() {\n  return (\n    <section className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-50 to-white\">\n      {/* Particle Effects Background */}\n      <div className=\"absolute inset-0 z-0\">\n        <div className=\"absolute right-0 top-1/2 -translate-y-1/2 w-1/2 h-full opacity-30\">\n          <Image\n            src=\"/particles.jpg\"\n            alt=\"Particle effects\"\n            fill\n            className=\"object-cover object-left\"\n            priority\n          />\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-6 lg:px-8 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Text Content */}\n          <div className=\"space-y-8\">\n            <div className=\"space-y-5\">\n              <h1 className=\"text-5xl lg:text-6xl font-medium text-gray-900 leading-tight\">\n                Install the Growth Infrastructure\n              </h1>\n              <p className=\"text-xl lg:text-2xl text-gray-700 leading-relaxed\">\n                Your Business{\" \"}\n                <span className=\"font-extralight text-gray-400\">Has Been Missing</span>\n              </p>\n            </div>\n          \n          </div>\n\n          {/* Right Column - Visual Space for Particles */}\n          <div className=\"hidden lg:block relative\">\n            <div className=\"w-full h-96 relative\">\n              {/* This space is intentionally left for the particle effects background */}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\n// Plug and Pay Systems Section Component\nfunction PlugAndPaySection() {\n  return (\n    <section className=\"py-12 bg-gray-100 h-140\">\n      <div className=\"container mx-auto px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-12 items-start\">\n          {/* Left Column - Visual/Image Space */}\n          <div className=\"bg-yellow-100 p-6 lg:p-8 h-150 flex items-center justify-center order-2 lg:order-1\">\n            {/* Placeholder for visual content */}\n          </div>\n\n          {/* Right Column - Content */}\n          <div className=\"space-y-6 order-1 lg:order-2\">\n            <div className=\"space-y-4\">\n              <p className=\"text-sm font-medium text-gray-500 uppercase tracking-wider\">\n                Ascenda Partners builds\n              </p>\n              <h2 className=\"text-4xl lg:text-9xl font-medium text-gray-900 leading-tight\">\n                PLUG AND PAY SYSTEMS\n              </h2>\n            </div>\n\n            <div className=\"space-y-4\">\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                <span className=\"font-medium text-gray-900\">\n                  that help you generate leads, close more deals, and scale with\n                  precision\n                </span>\n              </p>\n              <p className=\"text-base text-gray-600\">\n                No fluff. No retainers. Just automated growth machines —\n                installed in days.\n              </p>\n            </div>\n\n            \n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n\nexport default function Home() {\n  return (\n    <main>\n      <HeroSection />\n      <PlugAndPaySection />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,yBAAyB;AACzB,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,IAAI;wBACJ,WAAU;wBACV,QAAQ;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+D;;;;;;kDAG7E,8OAAC;wCAAE,WAAU;;4CAAoD;4CACjD;0DACd,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;sCAOtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3B;AAEA,yCAAyC;AACzC,SAAS;IACP,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAKf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDAA+D;;;;;;;;;;;;0CAK/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACX,cAAA,8OAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;kDAK9C,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrD;AAEe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC;;;;;;;;;;;AAGP", "debugId": null}}]}