{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "71f7f20c2cd12ab8806a256a053ada11", "previewModeSigningKey": "49fb70b544e4eaf40bb01568af323eda08754167eeced6917b37b04473ddb8ee", "previewModeEncryptionKey": "173e4e913d447a7d7a67f3f328e168f208e52bdc60ea98851d25cb7ca9d50b6f"}}