import Image from "next/image";

// Hero Section Component
function HeroSection() {
  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-50 to-white">
      {/* Particle Effects Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1/2 h-full opacity-30">
          <Image
            src="/particles.jpg"
            alt="Particle effects"
            fill
            className="object-cover object-left"
            priority
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <div className="space-y-5">
              <h1 className="text-5xl lg:text-6xl font-medium text-gray-900 leading-tight">
                Install the Growth Infrastructure
              </h1>
              <p className="text-xl lg:text-2xl text-gray-700 leading-relaxed">
                Your Business{" "}
                <span className="font-extralight text-gray-400">Has Been Missing</span>
              </p>
            </div>
          
          </div>

          {/* Right Column - Visual Space for Particles */}
          <div className="hidden lg:block relative">
            <div className="w-full h-96 relative">
              {/* This space is intentionally left for the particle effects background */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Plug and Pay Systems Section Component
function PlugAndPaySection() {
  return (
    <section className="py-12 bg-gray-100 h-190">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Visual/Image Space */}
          <div className="bg-yellow-100 p-8 lg:p-12 min-h-[800px] flex items-center justify-center">
            {/* Placeholder for visual content */}
          </div>

          {/* Right Column - Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <p className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                Ascenda Partners builds
              </p>
              <h2 className="text-4xl lg:text-9xl font-medium text-gray-900 leading-tight">
                PLUG AND PAY SYSTEMS
              </h2>
            </div>

            <p className="text-lg text-gray-600 leading-relaxed">
              <span className="font-medium text-gray-900">
                that help you generate leads, close more deals, and scale with
                precision
              </span>
            </p>
            {/* <div className="bg-yellow-100 p-8 lg:p-12 min-h-[250px] flex items-center justify-center"> */}
              <p className="text-base text-gray-600">
                No fluff. No retainers. Just automated growth machines —
                installed in days.
              </p>
            {/* </div> */}

            <div className="pt-4">
              <button
                type="button"
                className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200"
              >
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function Home() {
  return (
    <main>
      <HeroSection />
      <PlugAndPaySection />
    </main>
  );
}
