import Image from "next/image";

// Hero Section Component
function HeroSection() {
  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-50 to-white">
      {/* Particle Effects Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1/2 h-full opacity-30">
          <Image
            src="/particles.jpg"
            alt="Particle effects"
            fill
            className="object-cover object-left"
            priority
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <div className="space-y-5">
              <h1 className="text-5xl lg:text-2xl font-extralight text-gray-400 leading-tight">
                Ascenda
              </h1>
              <h1 className="text-5xl lg:text-6xl font-medium text-gray-900 leading-tight">
                Install the Growth Infrastructure
              </h1>
              <p className="text-xl lg:text-2xl text-gray-700 leading-relaxed">
                Your Business{" "}
                <span className="font-extralight text-gray-400">
                  Has Been Missing
                </span>
              </p>
            </div>
          </div>

          {/* Right Column - Visual Space for Particles */}
          <div className="hidden lg:block relative">
            <div className="w-full h-96 relative">
              {/* This space is intentionally left for the particle effects background */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Plug and Pay Systems Section Component
function PlugAndPaySection() {
  return (
    <section className="min-h-screen bg-gray-100 flex items-center">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Visual/Image Space */}
          <div className="bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-1">
            {/* Placeholder for visual content */}
          </div>

          {/* Right Column - Content */}
          <div className="space-y-6 order-1 lg:order-2">
            <div className="space-y-4">
              <p className="text-sm font-medium pt-3 text-gray-500 uppercase tracking-wider">
                Ascenda Partners builds
              </p>
              <h2 className="text-4xl lg:text-9xl font-medium text-gray-900 leading-tight">
                PLUG AND PAY SYSTEMS
              </h2>
            </div>

            <div className="space-y-4">
              <p className="text-lg text-gray-600 leading-relaxed">
                <span className="font-medium text-gray-900">
                  that help you generate leads, close more deals, and scale with
                  precision
                </span>
              </p>
              <p className="text-base text-gray-600 italic">
                No fluff. No retainers. Just automated growth machines —
                installed in days.
              </p>
            </div>

            
          </div>
        </div>
      </div>
    </section>
  );
}

// Credibility/Promise Bar Section Component
function CredibilitySection() {
  return (
    <section className="py-16 bg-white border-t border-gray-200">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
          <div className="space-y-3">
            {/* <div className="text-3xl">💼</div> */}
            <p className="text-sm font-medium text-gray-900">
              Built by systems architects, not service providers
            </p>
          </div>

          <div className="space-y-3">
            {/* <div className="text-3xl">⚙️</div> */}
            <p className="text-sm font-medium text-gray-900">
              Powered by AI, automation, and clean workflows
            </p>
          </div>

          <div className="space-y-3">
            {/* <div className="text-3xl">🧠</div> */}
            <p className="text-sm font-medium text-gray-900">
              Trusted by consultants, clinics, and scaling businesses
            </p>
          </div>

          <div className="space-y-3">
            {/* <div className="text-3xl">🚀</div> */}
            <p className="text-sm font-medium text-gray-900">
              Installed in &lt;14 days. Engineered for scale.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

// What We Do Section Component
function WhatWeDoSection() {
  return (
    <section className="min-h-screen bg-gray-100 flex items-center">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Content */}
          <div className="space-y-6 order-1 lg:order-1">
            <div className="space-y-6">
              <p className="text-sm pt-3 font-medium text-gray-500 uppercase tracking-wider">
                We Don't Sell Time
              </p>
              <h2 className="text-4xl lg:text-7xl font-medium text-gray-900 leading-[1.4]">
                WE INSTALL SYSTEMS THAT MULTIPLY IT.
              </h2>
            </div>

            <div className="space-y-4">
              <p className="text-lg text-gray-600 leading-relaxed">
                At Ascenda, we don't do retainers, vague strategies, or service
                packages.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                We install modular business systems that are fully automated,
                AI-powered, and designed to help you:
              </p>
            </div>

            <div className="space-y-3 pl-4">
              <div className="flex items-start gap-3">
                <span className="text-black font-bold">•</span>
                <span className="text-base text-gray-700">
                  Generate qualified leads{" "}
                  <span className="font-medium">(Kairo)</span>
                </span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-black font-bold">•</span>
                <span className="text-base text-gray-700">
                  Close more sales <span className="font-medium">(Airo)</span>
                </span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-black font-bold">•</span>
                <span className="text-base text-gray-700">
                  Deliver premium onboarding{" "}
                  <span className="font-medium">(Onboard)</span>
                </span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-black font-bold">•</span>
                <span className="text-base text-gray-700">
                  Increase lifetime value{" "}
                  <span className="font-medium">(Nurture, Retain)</span>
                </span>
              </div>
            </div>

            <div className="space-y-4">
              <p className="text-base text-gray-600 italic">
                Each system runs in the background — like a revenue engine that
                never stops.
              </p>

              <div className="pt-4">
                <button
                  type="button"
                  className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
                >
                  Book a call and we'll show you what system fits you best
                </button>
              </div>
            </div>
          </div>

          {/* Right Column - Visual Space */}
          <div className="bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-2">
            {/* Placeholder for system diagram or visual */}
          </div>
        </div>
      </div>
    </section>
  );
}

// Featured System Section Component
function FeaturedSystemSection() {
  return (
    <section className="py-20 bg-white overflow-hidden">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
          {/* KAIRO System Card */}
          <div className="bg-gray-50 p-8 lg:p-10 space-y-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
               style={{
                 animation: 'parallaxFloat1 8s ease-in-out infinite'
               }}>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <h3 className="text-2xl lg:text-7xl font-medium text-gray-900">
                  KAIRO
                </h3>
              </div>
              <h4 className="text-xl font-medium text-gray-900">
                Your Automated Growth Audit System
              </h4>
              <p className="text-base text-gray-600">
                Turn cold leads into warm calls using a self-diagnosing growth audit — powered by AI.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <span className="text-gray-900 font-bold">•</span>
                <span className="text-sm text-gray-700">Free value first.</span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-gray-900 font-bold">•</span>
                <span className="text-sm text-gray-700">High intent leads only.</span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-gray-900 font-bold">•</span>
                <span className="text-sm text-gray-700">Pre-qualifies your pipeline.</span>
              </div>
            </div>

            <div className="pt-4">
              <button
                type="button"
                className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-6 py-3 font-semibold transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
              >
Run Your Free Audit
              </button>
            </div>
          </div>

          {/* AIRO System Card */}
          <div className="bg-gray-50 p-8 lg:p-10 space-y-6 text-right shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
               style={{
                 animation: 'parallaxFloat2 8s ease-in-out infinite 3s'
               }}>
            <div className="space-y-4">
              <div className="flex items-center gap-3 justify-end">
                <h3 className="text-2xl lg:text-7xl font-medium text-gray-900">
                  AIRO
                </h3>
              </div>
              <h4 className="text-xl font-medium text-gray-900">
                Your AI Sales Prep System
              </h4>
              <p className="text-base text-gray-600">
                Help your sales team close faster by letting AI prep every call, every time.
              </p>
            </div>

            <div className="space-y-3">
              <div className="flex items-start gap-3 justify-end">
                <span className="text-sm text-gray-700">Fully customized CRM logic</span>
                <span className="text-gray-900 font-bold">•</span>
              </div>
              <div className="flex items-start gap-3 justify-end">
                <span className="text-sm text-gray-700">Competitor analysis</span>
                <span className="text-gray-900 font-bold">•</span>
              </div>
              <div className="flex items-start gap-3 justify-end">
                <span className="text-sm text-gray-700">Objection profiles and deal notes auto-generated</span>
                <span className="text-gray-900 font-bold">•</span>
              </div>
            </div>

            <div className="pt-4 flex justify-end">
              <button
                type="button"
                className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-6 py-3 font-semibold transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
              >
                See How Airo Works
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// How It Works Section Component
function HowItWorksSection() {
  return (
    <section className="py-20 bg-gray-100">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-7xl text-right font-medium text-gray-800 uppercase leading-tight mb-8">
            Built to Be Installed. Not Managed.
          </h2>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 lg:gap-12 mb-12">
          {/* Step 1 */}
          <div className="space-y-4">
            <div className="bg-white p-8 space-y-4">
              <h3 className="text-xl font-bold text-gray-900">Diagnose</h3>
              <p className="text-base text-gray-600">
                We identify the best-fit system for your business.
              </p>
            </div>
          </div>

          {/* Step 2 */}
          <div className="text-center space-y-4">
            <div className="bg-white p-8 space-y-4">
              <h3 className="text-xl font-bold text-gray-900">Install</h3>
              <p className="text-base text-gray-600">
                Our team builds and customizes it in under 2 weeks.
              </p>
            </div>
          </div>

          {/* Step 3 */}
          <div className="text-center space-y-4">
            <div className="bg-white p-8 space-y-4 text-right">
              <h3 className="text-xl font-bold text-gray-900">Run</h3>
              <p className="text-base text-gray-600">
                The system operates automatically. You monitor the dashboard. We
                handle the rest.
              </p>
            </div>
          </div>
        </div>

        <div className="text-center ">
          <button
            type="button"
            className="bg-gray-900 cursor-pointer hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
          >
            Book Your System Assessment
          </button>
        </div>
      </div>
    </section>
  );
}

// Why Ascenda Differentiator Section Component
function WhyAscendaSection() {
  return (
    <section className="py-20 bg-white border-t border-gray-200">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
            Ascenda vs. The Rest
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Productized AI Systems</h3>
          </div>

          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Built in days, not months</h3>
          </div>

          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Designed to scale automatically</h3>
          </div>

          <div className="text-center space-y-3">
            <div className="text-2xl text-green-600">✔</div>
            <h3 className="font-semibold text-gray-900">Boutique, high-end experience</h3>
          </div>
        </div>
      </div>
    </section>
  );
}

// Testimonials Section Component
function TestimonialsSection() {
  return (
    <section className="py-20 bg-gray-100">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12">
          <div className="bg-white p-8 lg:p-10 space-y-6">
            <p className="text-lg text-gray-700 italic leading-relaxed">
              "Since we installed Airo, our close rate went up 33% — without hiring anyone new."
            </p>
            <div className="border-t pt-4">
              <p className="font-semibold text-gray-900">Sarah T.</p>
              <p className="text-sm text-gray-600">Consultant, Dubai</p>
            </div>
          </div>

          <div className="bg-white p-8 lg:p-10 space-y-6">
            <p className="text-lg text-gray-700 italic leading-relaxed">
              "Kairo gave us 40 leads in 3 weeks — and 9 booked calls. All automated."
            </p>
            <div className="border-t pt-4">
              <p className="font-semibold text-gray-900">David R.</p>
              <p className="text-sm text-gray-600">Agency Owner</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Final CTA Section Component
function FinalCTASection() {
  return (
    <section className="min-h-screen bg-gray-900 flex items-center">
      <div className="container mx-auto px-6 lg:px-8 text-center">
        <div className="max-w-4xl mx-auto space-y-8">
          <h2 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
            Let's Turn Your Business Into a Machine
          </h2>

          <p className="text-xl text-gray-300 leading-relaxed max-w-3xl mx-auto">
            If you're tired of manual hustle, inconsistent leads, and bloated agencies…
            It's time to install real growth infrastructure.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
            <button
              type="button"
              className="bg-white cursor-pointer hover:bg-gray-100 text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white hover:border-gray-100"
            >
             Run Your Free Growth Audit
            </button>
            <button
              type="button"
              className="bg-transparent cursor-pointer hover:bg-white text-white hover:text-gray-900 px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-white"
            >
            Book a Discovery Call
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}

export default function Home() {
  return (
    <main>
      <HeroSection />
      <PlugAndPaySection />
      <CredibilitySection />
      <WhatWeDoSection />
      <FeaturedSystemSection />
      <HowItWorksSection />
      <WhyAscendaSection />
      <TestimonialsSection />
      <FinalCTASection />
    </main>
  );
}
