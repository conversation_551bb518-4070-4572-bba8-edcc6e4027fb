import Image from "next/image";

// Hero Section Component
function HeroSection() {
  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-gray-50 to-white">
      {/* Particle Effects Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute right-0 top-1/2 -translate-y-1/2 w-1/2 h-full opacity-30">
          <Image
            src="/particles.jpg"
            alt="Particle effects"
            fill
            className="object-cover object-left"
            priority
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <div className="space-y-5">
              <h1 className="text-5xl lg:text-6xl font-medium text-gray-900 leading-tight">
                Install the Growth Infrastructure
              </h1>
              <p className="text-xl lg:text-2xl text-gray-700 leading-relaxed">
                Your Business{" "}
                <span className="font-extralight text-gray-400">Has Been Missing</span>
              </p>
            </div>
          
          </div>

          {/* Right Column - Visual Space for Particles */}
          <div className="hidden lg:block relative">
            <div className="w-full h-96 relative">
              {/* This space is intentionally left for the particle effects background */}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

// Plug and Pay Systems Section Component
function PlugAndPaySection() {
  return (
    <section className="min-h-screen bg-gray-100 flex items-center">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Visual/Image Space */}
          <div className="bg-yellow-100 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-1">
            {/* Placeholder for visual content */}
          </div>

          {/* Right Column - Content */}
          <div className="space-y-6 order-1 lg:order-2">
            <div className="space-y-4">
              <p className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                Ascenda Partners builds
              </p>
              <h2 className="text-4xl lg:text-9xl font-medium text-gray-900 leading-tight">
                PLUG AND PAY SYSTEMS
              </h2>
            </div>

            <div className="space-y-4">
              <p className="text-lg text-gray-600 leading-relaxed">
                <span className="font-medium text-gray-900">
                  that help you generate leads, close more deals, and scale with
                  precision
                </span>
              </p>
              <p className="text-base text-gray-600 italic">
                No fluff. No retainers. Just automated growth machines —
                installed in days.
              </p>
            </div>

            
          </div>
        </div>
      </div>
    </section>
  );
}

// Credibility/Promise Bar Section Component
function CredibilitySection() {
  return (
    <section className="py-16 bg-white border-t border-gray-200">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
          <div className="space-y-3">
            <div className="text-3xl">💼</div>
            <p className="text-sm font-medium text-gray-900">
              Built by systems architects, not service providers
            </p>
          </div>

          <div className="space-y-3">
            <div className="text-3xl">⚙️</div>
            <p className="text-sm font-medium text-gray-900">
              Powered by AI, automation, and clean workflows
            </p>
          </div>

          <div className="space-y-3">
            <div className="text-3xl">🧠</div>
            <p className="text-sm font-medium text-gray-900">
              Trusted by consultants, clinics, and scaling businesses
            </p>
          </div>

          <div className="space-y-3">
            <div className="text-3xl">🚀</div>
            <p className="text-sm font-medium text-gray-900">
              Installed in &lt;14 days. Engineered for scale.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

// What We Do Section Component
function WhatWeDoSection() {
  return (
    <section className="min-h-screen bg-gray-100 flex items-center">
      <div className="container mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left Column - Content */}
          <div className="space-y-6 order-1 lg:order-1">
            <div className="space-y-4">
              <h2 className="text-4xl lg:text-6xl font-medium text-gray-900 leading-tight">
                We Don't Sell Time. We Install Systems That Multiply It.
              </h2>
            </div>

            <div className="space-y-4">
              <p className="text-lg text-gray-600 leading-relaxed">
                At Ascenda, we don't do retainers, vague strategies, or service packages.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                We install modular business systems that are fully automated, AI-powered, and designed to help you:
              </p>
            </div>

            <div className="space-y-3 pl-4">
              <div className="flex items-start gap-3">
                <span className="text-blue-600 font-bold">•</span>
                <span className="text-base text-gray-700">Generate qualified leads <span className="font-medium">(Kairo)</span></span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-blue-600 font-bold">•</span>
                <span className="text-base text-gray-700">Close more sales <span className="font-medium">(Airo)</span></span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-blue-600 font-bold">•</span>
                <span className="text-base text-gray-700">Deliver premium onboarding <span className="font-medium">(Onboard)</span></span>
              </div>
              <div className="flex items-start gap-3">
                <span className="text-blue-600 font-bold">•</span>
                <span className="text-base text-gray-700">Increase lifetime value <span className="font-medium">(Nurture, Retain)</span></span>
              </div>
            </div>

            <div className="space-y-4">
              <p className="text-base text-gray-600 italic">
                Each system runs in the background — like a revenue engine that never stops.
              </p>

              <div className="pt-4">
                <button
                  type="button"
                  className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-4 font-semibold text-lg transition-all duration-200 border-2 border-gray-900 hover:border-gray-800"
                >
                  Book a call and we'll show you what system fits you best
                </button>
              </div>
            </div>
          </div>

          {/* Right Column - Visual Space */}
          <div className="bg-blue-50 p-6 lg:p-8 h-180 flex items-center justify-center order-2 lg:order-2">
            {/* Placeholder for system diagram or visual */}
          </div>
        </div>
      </div>
    </section>
  );
}

export default function Home() {
  return (
    <main>
      <HeroSection />
      <PlugAndPaySection />
      <CredibilitySection />
      <WhatWeDoSection />
    </main>
  );
}
