@import "tailwindcss";

@theme inline {
  --font-sans: var(--font-dm-sans);
}

/* Parallax floating animations */
@keyframes parallaxFloat1 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes parallaxFloat2 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes parallaxFloat3 {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-12px); }
}

/* Typing animation */
@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #374151; }
}

.typewriter {
  overflow: hidden;
  border-right: 3px solid #374151;
  white-space: nowrap;
  animation:
    typing 3.5s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

.typewriter-line2 {
  overflow: hidden;
  border-right: 3px solid #374151;
  white-space: nowrap;
  animation:
    typing 3s steps(35, end) 1s,
    blink-caret 0.75s step-end infinite 1s;
  animation-fill-mode: both;
}

.typewriter-line3 {
  overflow: hidden;
  border-right: 3px solid #374151;
  white-space: nowrap;
  animation:
    typing 2.5s steps(30, end) 2.5s,
    blink-caret 0.75s step-end infinite 2.5s;
  animation-fill-mode: both;
}
